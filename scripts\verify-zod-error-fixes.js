#!/usr/bin/env node

/**
 * 验证 ZodError 修复脚本
 * 检查所有 ZodError.errors 是否已正确修改为 ZodError.issues
 */

const fs = require('fs')
const path = require('path')
const glob = require('glob')

// 检查文件内容
function checkFileForZodErrorIssues(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    
    // 检查是否包含错误的 error.errors 用法
    const hasErrorErrors = content.includes('error.errors') && content.includes('ZodError')
    
    // 检查是否包含正确的 error.issues 用法
    const hasErrorIssues = content.includes('error.issues') && content.includes('ZodError')
    
    return {
      filePath,
      hasErrorErrors,
      hasErrorIssues,
      content: content
    }
  } catch (error) {
    console.error(`无法读取文件 ${filePath}:`, error.message)
    return null
  }
}

// 扫描所有 TypeScript 文件
function scanAllFiles() {
  console.log('🔍 扫描所有 TypeScript 文件中的 ZodError 使用情况...\n')
  
  const patterns = [
    'app/**/*.ts',
    'app/**/*.tsx',
    'lib/**/*.ts',
    'utils/**/*.ts',
    'components/**/*.ts',
    'components/**/*.tsx'
  ]
  
  let allFiles = []
  patterns.forEach(pattern => {
    const files = glob.sync(pattern, { ignore: ['node_modules/**', '.next/**'] })
    allFiles = allFiles.concat(files)
  })
  
  const results = []
  let hasErrors = false
  
  allFiles.forEach(file => {
    const result = checkFileForZodErrorIssues(file)
    if (result && (result.hasErrorErrors || result.hasErrorIssues)) {
      results.push(result)
      
      if (result.hasErrorErrors) {
        hasErrors = true
        console.log(`❌ ${file} - 仍然使用 error.errors`)
      } else if (result.hasErrorIssues) {
        console.log(`✅ ${file} - 正确使用 error.issues`)
      }
    }
  })
  
  return { results, hasErrors }
}

// 验证特定文件的修复
function verifySpecificFixes() {
  console.log('\n🔧 验证特定文件的 ZodError 修复...\n')
  
  const filesToCheck = [
    'app/api/auth/jwt-token/route.ts',
    'app/api/user-behavior/activations/route.ts',
    'app/api/user-behavior/device-connections/route.ts'
  ]
  
  let allFixed = true
  
  filesToCheck.forEach(file => {
    const result = checkFileForZodErrorIssues(file)
    if (result) {
      if (result.hasErrorErrors) {
        console.log(`❌ ${file} - 仍然使用 error.errors`)
        allFixed = false
      } else if (result.hasErrorIssues) {
        console.log(`✅ ${file} - 已修复为 error.issues`)
      } else {
        console.log(`ℹ️  ${file} - 未使用 ZodError`)
      }
    }
  })
  
  return allFixed
}

// 生成修复报告
function generateFixReport(scanResults) {
  console.log('\n📊 ZodError 修复报告\n')
  console.log('='.repeat(50))
  
  const { results, hasErrors } = scanResults
  
  if (hasErrors) {
    console.log('❌ 发现需要修复的文件:')
    results.forEach(result => {
      if (result.hasErrorErrors) {
        console.log(`   - ${result.filePath}`)
      }
    })
  } else {
    console.log('✅ 所有 ZodError 使用都已正确修复!')
  }
  
  console.log(`\n📈 统计信息:`)
  console.log(`   - 扫描的文件总数: ${results.length}`)
  console.log(`   - 使用 ZodError 的文件: ${results.length}`)
  console.log(`   - 已修复的文件: ${results.filter(r => r.hasErrorIssues && !r.hasErrorErrors).length}`)
  console.log(`   - 需要修复的文件: ${results.filter(r => r.hasErrorErrors).length}`)
  
  return !hasErrors
}

// 主函数
async function main() {
  console.log('🚀 开始验证 ZodError 修复\n')
  
  // 验证特定文件
  const specificFixesOk = verifySpecificFixes()
  
  // 扫描所有文件
  const scanResults = scanAllFiles()
  
  // 生成报告
  const allFixed = generateFixReport(scanResults)
  
  console.log('\n' + '='.repeat(50))
  
  if (allFixed && specificFixesOk) {
    console.log('🎉 所有 ZodError 修复验证通过!')
    console.log('\n✅ 修复内容:')
    console.log('1. ✅ 将 error.errors 修改为 error.issues')
    console.log('2. ✅ 保持 ZodError 类型检查不变')
    console.log('3. ✅ 错误响应格式保持一致')
    
    console.log('\n🚀 现在项目应该可以正常构建了!')
    process.exit(0)
  } else {
    console.log('❌ ZodError 修复验证失败!')
    console.log('\n💡 需要手动修复的问题:')
    console.log('1. 将所有 error.errors 改为 error.issues')
    console.log('2. 确保 ZodError 导入正确')
    console.log('3. 重新运行此脚本验证')
    process.exit(1)
  }
}

// 运行验证
main().catch(console.error)
